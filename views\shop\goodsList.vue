<template>
    <view class="goods-list-page">
        <!-- 头部信息 -->
        <view class="header-info" v-if="userInfo">
            <view class="user-avatar">
                <image :src="info.headimgurl" mode="aspectFill"></image>
            </view>
            <view class="user-detail">
                <view class="user-name">{{ info.nickname || '用户' }}的橱窗</view>
                <view class="goods-count">共{{ total }}件商品</view>
            </view>
            <!-- 发布商品按钮 - 仅自己的橱窗显示 -->
            <view class="publish-btn" v-if="isOwnShop" @click.stop="goPublish">
                <u-icon name="plus" color="#8966ef" size="32"></u-icon>
                <text>发布</text>
            </view>
            <view class="header-decoration">
                <view class="decoration-circle circle-1"></view>
                <view class="decoration-circle circle-2"></view>
            </view>
        </view>

        <!-- 筛选标签 -->
        <view class="filter-tabs" v-if="isOwnShop">
            <view class="tab-item" :class="{ active: currentStatus === item.value }" v-for="item in statusTabs" :key="item.value" @click="switchStatus(item.value)">
                {{ item.label }}
            </view>
        </view>

        <!-- 商品列表 -->
        <view class="goods-list">
            <view class="goods-item" v-for="(item, index) in goodsList" :key="item.id" @click="goDetail(item)">
                <!-- 商品图片 -->
                <view class="goods-image">
                    <u-image width="200rpx" height="200rpx" :src="item.cover_img" mode="aspectFill" border-radius="12rpx"></u-image>
                    <!-- 状态标识 -->
                    <view class="status-badge" v-if="item.status === '2'">已下架</view>
                </view>

                <!-- 商品信息 -->
                <view class="goods-info">
                    <view class="goods-title u-line-2">{{ item.title }}</view>
                    <!-- <view class="goods-time">{{ $util.formatTimeString(item.addtime)  }}发布</view> -->

                    <!-- 价格信息 -->
                    <view class="price-info" v-if="item.sku_list && item.sku_list.length > 0">
                        <view class="current-price">
                            <text class="price-symbol">¥</text>
                            <text class="price-value">{{ getMinPrice(item.sku_list) }}</text>
                            <text class="price-suffix" v-if="hasMultiplePrice(item.sku_list)">起</text>
                        </view>
                        <view class="original-price" v-if="getMinOriginalPrice(item.sku_list) > getMinPrice(item.sku_list)">¥{{ getMinOriginalPrice(item.sku_list) }}</view>
                    </view>

                    <!-- 浏览量 -->
                    <view class="view-count">
                        <u-icon name="eye" size="24" color="#999"></u-icon>
                        <text>{{ item.view_num || 0 }}次浏览</text>
                    </view>
                </view>

                <!-- 操作按钮 - 仅自己的橱窗显示 -->
                <view class="action-buttons" v-if="isOwnShop" @click.stop>
                    <view class="action-btn edit-btn" @click="editGoods(item)">
                        <u-icon name="edit-pen" size="28" color="#8966ef"></u-icon>
                        <text>编辑</text>
                    </view>
                    <view class="action-btn status-btn" :class="{ 'off-shelf': item.status === '2' }" @click="toggleStatus(item, index)">
                        <u-icon :name="item.status === '1' ? 'pause-circle' : 'play-circle'" size="28" :color="item.status === '1' ? '#f0ad4e' : '#4cd964'"></u-icon>
                        <text>{{ item.status === '1' ? '下架' : '上架' }}</text>
                    </view>
                </view>
            </view>
        </view>
        <vip-popup v-model="showVip" @member-pay="handleMemberPay" @close="handleClose"></vip-popup>

        <!-- 空状态 -->
        <empty-state :show="goodsList.length === 0 && !loading" type="goods" :tip="isOwnShop ? '无下架商品' : '该用户还没有发布商品'"></empty-state>

        <!-- 加载更多 -->
        <u-loadmore :status="loadmoreStatus" v-if="goodsList.length > 0"></u-loadmore>
    </view>
</template>

<script>
import { mapState } from 'vuex';
import { getUserGoodList, upGoodStatus } from '@/utils/vmeitime-http/shop.js';
import EmptyState from '@/components/empty-state.vue';
import vipPopup from '@/components/vip-popup.vue';

export default {
    components: {
        EmptyState,
        vipPopup,
    },
    data() {
        return {
            showVip: false,

            targetUserId: null, // 要查看的用户ID
            isOwnShop: false, // 是否是自己的橱窗

            // 商品列表数据
            goodsList: [],
            total: 0,
            currentStatus: '0', // 当前筛选状态：0-全部，1-上架，2-下架

            // 分页数据
            page: 1,
            pagesize: 10,
            hasMore: true,
            loading: false,
            loadmoreStatus: 'loadmore',

            // 状态标签
            statusTabs: [
                { label: '全部', value: '0' },
                { label: '上架中', value: '1' },
                { label: '已下架', value: '2' },
            ],
            info: {},
        };
    },
    computed: {
        ...mapState(['isValidMember', 'userInfo']),
    },
    onLoad(options) {
        // 获取要查看的用户ID，如果没有则查看自己的
        this.targetUserId = options.userId || this.userInfo.uid;
        this.isOwnShop = !options.userId || options.userId == this.userInfo.uid;

        this.initPage();
    },
    onShow() {
        // 从发布页面返回时刷新列表
        if (this.isOwnShop) {
            this.refreshList();
        }
    },
    onPullDownRefresh() {
        this.refreshList();
    },
    onReachBottom() {
        this.loadMore();
    },
    methods: {
        // 初始化页面
        async initPage() {
            await this.getUserInfo();
            this.loadGoodsList();
        },

        // 获取用户信息
        async getUserInfo() {
            if (this.isOwnShop) {
                this.info = this.userInfo;
            } else {
                this.getOtherUserInfo();
            }
        },

        async getOtherUserInfo() {
            try {
                const res = await this.$api.getOtherUserInfo({
                    access_token: uni.getStorageSync('token'),
                    to_user_id: this.targetUserId,
                });

                this.info = res.data;
            } catch (error) {
                console.error('获取用户信息失败:', error);
            }
        },

        // 加载商品列表
        async loadGoodsList(isRefresh = false) {
            if (this.loading) return;

            this.loading = true;
            if (isRefresh) {
                this.page = 1;
                this.hasMore = true;
            }

            try {
                const params = {
                    access_token: uni.getStorageSync('token'),
                    page: this.page,
                    pagesize: this.pagesize,
                    status: this.currentStatus,
                };

                // 如果不是自己的橱窗，添加用户ID参数
                if (!this.isOwnShop) {
                    params.web_user_id = this.targetUserId;
                }

                const res = await getUserGoodList(params);

                if (res.status === 0) {
                    const newList = res.data.list || [];

                    if (isRefresh) {
                        this.goodsList = newList;
                    } else {
                        this.goodsList = [...this.goodsList, ...newList];
                    }

                    this.total = parseInt(res.data.total) || 0;
                    this.hasMore = newList.length >= this.pagesize;

                    if (this.hasMore) {
                        this.page++;
                        this.loadmoreStatus = 'loadmore';
                    } else {
                        this.loadmoreStatus = 'nomore';
                    }
                } else {
                    uni.showToast({
                        title: res.msg || '获取商品列表失败',
                        icon: 'none',
                    });
                }
            } catch (error) {
                console.error('获取商品列表失败:', error);
                uni.showToast({
                    title: '网络错误，请重试',
                    icon: 'none',
                });
            } finally {
                this.loading = false;
                uni.stopPullDownRefresh();
            }
        },

        // 刷新列表
        refreshList() {
            this.loadGoodsList(true);
        },

        // 加载更多
        loadMore() {
            if (this.hasMore && !this.loading) {
                this.loadmoreStatus = 'loading';
                this.loadGoodsList();
            }
        },

        // 切换状态筛选
        switchStatus(status) {
            if (this.currentStatus === status) return;

            this.currentStatus = status;
            this.refreshList();
        },

        // 获取最低价格
        getMinPrice(skuList) {
            if (!skuList || skuList.length === 0) return '0.00';
            const prices = skuList.map(sku => parseFloat(sku.price || 0));
            return Math.min(...prices).toFixed(2);
        },

        // 获取最低原价
        getMinOriginalPrice(skuList) {
            if (!skuList || skuList.length === 0) return 0;
            const prices = skuList.map(sku => parseFloat(sku.org_price || 0));
            return Math.min(...prices).toFixed(2);
        },

        // 是否有多个价格
        hasMultiplePrice(skuList) {
            if (!skuList || skuList.length <= 1) return false;
            const prices = skuList.map(sku => parseFloat(sku.price || 0));
            return Math.max(...prices) > Math.min(...prices);
        },

        // 跳转到商品详情
        goDetail(item) {
            uni.navigateTo({
                url: `/views/shop/detail?id=${item.id}`,
            });
        },

        // 跳转到发布页面
        goPublish() {
            console.log(111111111111111111);

            console.log(this.isValidMember);
            if (!this.isValidMember) {
                this.showVip = true;
                return;
            }
            uni.navigateTo({
                url: '/views/shop/shopPublish',
            });
        },

        // 编辑商品
        editGoods(item) {
            uni.navigateTo({
                url: `/views/shop/shopPublish?id=${item.id}&type=edit`,
            });
        },

        // 切换商品状态（上架/下架）
        async toggleStatus(item, index) {
            const newStatus = item.status === '1' ? '2' : '1';
            const actionText = newStatus === '1' ? '上架' : '下架';

            try {
                uni.showLoading({
                    title: `${actionText}中...`,
                });

                const res = await upGoodStatus({
                    access_token: uni.getStorageSync('token'),
                    user_good_id: item.id,
                    status: newStatus,
                });

                // 更新本地数据
                this.goodsList[index].status = newStatus;

                uni.showToast({
                    title: `${actionText}成功`,
                    icon: 'success',
                });
            } catch (error) {
                console.error(`${actionText}失败:`, error);
                uni.showToast({
                    title: `${actionText}失败，请重试`,
                    icon: 'none',
                });
            } finally {
                uni.hideLoading();
            }
        },

        // 处理会员支付
        async handleMemberPay(paymentData) {
            console.log('会员支付:', paymentData);

            try {
                uni.showLoading({
                    title: '处理中...',
                });

                // 创建会员订单
                const orderRes = await this.$api.createMemberOrder({
                    good_id: paymentData.product.id,
                    num: 1,
                    access_token: uni.getStorageSync('token'),
                });

                if (orderRes.status !== 0) {
                    throw new Error(orderRes.msg || '创建订单失败');
                }

                // 获取用户openid（小程序支付需要）
                const openid = this.userInfo.openid;
                if (!openid) {
                    throw new Error('获取用户信息失败，请重新登录');
                }

                // 调用支付接口
                const payRes = await this.$api.createMiniPay({
                    openid: openid,
                    orderid: orderRes.data.order_no,
                    access_token: uni.getStorageSync('token'),
                });

                if (payRes.status !== 0) {
                    throw new Error(payRes.msg || '支付失败');
                }

                // 调用微信支付
                await this.requestPayment(payRes.data);

                uni.hideLoading();
                uni.showToast({
                    title: '开通成功',
                    icon: 'success',
                });

                // 更新用户信息
                await this.VAgetUser();
            } catch (error) {
                console.error('会员支付失败:', error);
                uni.hideLoading();
                uni.showToast({
                    title: error.message || '支付失败，请重试',
                    icon: 'none',
                });
            }
        },

        // 调用微信支付
        async requestPayment(paymentData) {
            return new Promise((resolve, reject) => {
                uni.requestPayment({
                    provider: 'wxpay',
                    timeStamp: paymentData.timeStamp,
                    nonceStr: paymentData.nonceStr,
                    package: paymentData.package,
                    signType: paymentData.signType,
                    paySign: paymentData.paySign,
                    success: res => {
                        console.log('支付成功:', res);
                        resolve(res);
                    },
                    fail: err => {
                        console.error('支付失败:', err);
                        reject(new Error('支付失败'));
                    },
                });
            });
        },
        handleClose() {
            this.showVip = false;
        },
    },
};
</script>

<style lang="scss" scoped>
// 引入项目主题色变量
$theme-primary: #8966ef;
$theme-primary-light: #a584f2;
$theme-secondary: #f0cc6c;
$border-color: #f0f0f0;
$text-color: #333;
$text-gray: #666;
$text-light: #999;
$bg-gray: #f7f8f7;
.goods-list-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8f6ff 0%, #fff5e6 100%);
    padding-bottom: 20rpx;
}

// 头部信息
.header-info {
    background: linear-gradient(135deg, #8966ef, #a584f2, #f0cc6c);
    padding: 40rpx 30rpx 30rpx;
    display: flex;
    align-items: center;
    color: white;
    position: relative;
    .user-avatar {
        width: 100rpx;
        height: 100rpx;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 20rpx;
        border: 3rpx solid rgba(255, 255, 255, 0.3);

        image {
            width: 100%;
            height: 100%;
        }
    }

    .user-detail {
        flex: 1;

        .user-name {
            font-size: 36rpx;
            font-weight: 600;
            margin-bottom: 8rpx;
        }

        .goods-count {
            font-size: 26rpx;
            opacity: 0.9;
        }
    }

    .publish-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 16rpx 20rpx;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 12rpx;
        backdrop-filter: blur(10rpx);
        z-index: 99;
        text {
            font-size: 24rpx;
            margin-top: 4rpx;
        }
    }

    .header-decoration {
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        z-index: 1;

        .decoration-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);

            &.circle-1 {
                width: 120rpx;
                height: 120rpx;
                top: -30rpx;
                right: 50rpx;
                animation: float 6s ease-in-out infinite;
            }

            &.circle-2 {
                width: 80rpx;
                height: 80rpx;
                top: 60rpx;
                right: 150rpx;
                animation: float 4s ease-in-out infinite reverse;
            }
        }
    }
}

// 筛选标签
.filter-tabs {
    display: flex;
    background: white;
    padding: 0 30rpx;
    border-bottom: 1rpx solid #f0f1f5;

    .tab-item {
        flex: 1;
        text-align: center;
        padding: 30rpx 0;
        font-size: 28rpx;
        color: #666;
        position: relative;

        &.active {
            color: #8966ef;
            font-weight: 600;

            &::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 60rpx;
                height: 4rpx;
                background: #8966ef;
                border-radius: 2rpx;
            }
        }
    }
}

// 商品列表
.goods-list {
    padding: 20rpx 30rpx;

    .goods-item {
        background: white;
        border-radius: 16rpx;
        padding: 30rpx;
        margin-bottom: 20rpx;
        display: flex;
        box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
        border: 1rpx solid #f0f1f5;
        transition: all 0.3s ease;

        &:active {
            transform: scale(0.98);
            box-shadow: 0 4rpx 20rpx rgba(137, 102, 239, 0.1);
        }

        .goods-image {
            position: relative;
            margin-right: 24rpx;

            .status-badge {
                position: absolute;
                top: -8rpx;
                right: -8rpx;
                background: #f0ad4e;
                color: white;
                font-size: 20rpx;
                padding: 4rpx 8rpx;
                border-radius: 8rpx;
                transform: scale(0.9);
            }
        }

        .goods-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .goods-title {
                font-size: 32rpx;
                color: #333;
                font-weight: 500;
                line-height: 1.4;
                margin-bottom: 12rpx;
            }

            .goods-time {
                font-size: 24rpx;
                color: #999;
                margin-bottom: 16rpx;
            }

            .price-info {
                display: flex;
                align-items: baseline;
                margin-bottom: 12rpx;

                .current-price {
                    display: flex;
                    align-items: baseline;
                    color: #8966ef;

                    .price-symbol {
                        font-size: 24rpx;
                        margin-right: 2rpx;
                    }

                    .price-value {
                        font-size: 36rpx;
                        font-weight: 600;
                    }

                    .price-suffix {
                        font-size: 24rpx;
                        margin-left: 4rpx;
                    }
                }

                .original-price {
                    font-size: 24rpx;
                    color: #999;
                    text-decoration: line-through;
                    margin-left: 16rpx;
                }
            }

            .view-count {
                display: flex;
                align-items: center;
                font-size: 24rpx;
                color: #999;

                text {
                    margin-left: 8rpx;
                }
            }
        }

        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 16rpx;
            margin-left: 20rpx;

            .action-btn {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 16rpx 20rpx;
                border-radius: 12rpx;
                border: 1rpx solid #e5e5e5;
                background: #fafafa;
                min-width: 100rpx;

                text {
                    font-size: 22rpx;
                    margin-top: 4rpx;
                    color: #666;
                }

                &.edit-btn {
                    border-color: rgba(137, 102, 239, 0.3);
                    background: rgba(137, 102, 239, 0.05);

                    text {
                        color: #8966ef;
                    }
                }

                &.status-btn {
                    border-color: rgba(76, 201, 100, 0.3);
                    background: rgba(76, 201, 100, 0.05);

                    text {
                        color: #4cd964;
                    }

                    &.off-shelf {
                        border-color: rgba(240, 173, 78, 0.3);
                        background: rgba(240, 173, 78, 0.05);

                        text {
                            color: #f0ad4e;
                        }
                    }
                }
            }
        }
    }
}

// 浮动动画
@keyframes float {
    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20rpx) rotate(180deg);
    }
}
</style>
