<template>
    <view class="moment-detail">
        <!-- 导航栏 -->
        <!-- <u-navbar :title="workInfo.user.nickname" :autoBack="true" fixed safe-area-inset-top class="custom-navbar" titleStyle="font-weight: bold; font-size: 32rpx; color: #ffffff;">
        </u-navbar> -->

        <!-- 页面内容区域 -->
        <view class="content">
            <!-- 个人信息区域 -->
            <view class="profile-info">
                <!-- 用户基本信息区域 -->
                <view class="user-info-section">
                    <!-- 头像和基本信息 -->
                    <view class="user-main-info">
                        <view class="avatar-container" @click="goToUserProfile">
                            <u-avatar :src="workInfo.user.headimgurl" size="150" mode="aspectFill" class="custom-avatar"></u-avatar>
                        </view>
                        <view class="info-container">
                            <!-- 第一行：昵称和在线时间 -->
                            <view class="user-nickname-row">
                                <text class="user-nickname">{{ workInfo.user.nickname }}</text>
                                <text class="online-time" v-if="workInfo.addtime">{{ $util.formatTimeString(workInfo.addtime) }}</text>
                            </view>

                            <!-- 第二行：性别、年龄、地区 -->
                            <view class="user-detail-row">
                                <view class="gender-tag" :class="{ male: isMale, female: !isMale }">
                                    <u-icon :name="isMale ? 'man' : 'woman'" size="24" color="#fff"></u-icon>
                                </view>
                                <view class="info-tag" v-if="generationLabel">
                                    <u-icon name="calendar" color="#8966ef" size="26"></u-icon>
                                    <text>{{ generationLabel }}</text>
                                </view>
                                <view class="info-tag location-tag" v-if="fullLocation" :title="fullLocation">
                                    <u-icon name="map-fill" color="#8966ef" size="26"></u-icon>
                                    <text class="location-text">{{ fullLocation }}</text>
                                </view>
                            </view>
                        </view>
                    </view>

                    <!-- 右上角点赞按钮 -->
                </view>

                <!-- 个人简介 -->
                <view class="user-intro" v-if="workInfo.user.note">
                    <text>{{ workInfo.user.note }}</text>
                </view>

                <!-- 标签列表 -->
                <view class="user-tags" v-if="hasTags">
                    <view>
                        <view class="tag-item" v-for="(tag, index) in workInfo.user.tag_data_arr" :key="tag.id">
                            <text>{{ tag.name }}</text>
                        </view>
                    </view>
                    <view class="like-button-container" v-if="!isOwnMoment">
                        <view class="like-button" @click="handleLike" :class="{ liked: workInfo.is_like }">
                            <u-icon :name="workInfo.is_like ? 'heart-fill' : 'heart'" :color="workInfo.is_like ? '#ff6b6b' : '#999'" size="32"></u-icon>
                        </view>
                    </view>
                </view>
                <!-- 分隔线 -->
                <view class="divider"></view>
                <!-- 操作按钮区域 -->
                <view class="action-buttons-section">
                    <!-- 如果不是自己的动态，显示操作按钮 -->
                    <view class="action-buttons" v-if="!isOwnMoment">
                        <button class="gradient-btn private-chat-btn" @click="handlePrivateChat" hover-class="btn-hover">
                            <text>私聊</text>
                        </button>
                        <button class="gradient-btn wechat-btn" @click="showVipPopup" hover-class="btn-hover">
                            <text>{{ isValidMember ? '查看微信' : '加微信' }}</text>
                        </button>
                        <button class="gradient-btn gift-btn" @click="showRewardPopup" hover-class="btn-hover">
                            <text>送礼物</text>
                        </button>
                    </view>
                    <!-- 如果是自己的动态，显示操作按钮 -->
                    <view class="action-buttons own-moment-actions" v-else>
                        <button class="gradient-btn status-btn" v-if="Number(workInfo.status) !== 3" @click="showStatusActionSheet" hover-class="btn-hover">
                            <u-icon name="setting" size="20" color="#ffffff"></u-icon>
                            <text>{{ getStatusText() }}</text>
                        </button>
                    </view>
                </view>
            </view>

            <!-- 动态内容区域 -->
            <view class="moment-content">
                <view class="section-header">
                    <u-icon name="chat-fill" size="20" color="#8966ef"></u-icon>
                    <text class="section-title">动态内容</text>
                </view>
                <!-- <view class="moment-tags">
                    <view class="moment-tag">
                        <u-icon name="photo" size="16" color="#8966ef"></u-icon>
                        <text>生活记录</text>
                    </view>
                    <view class="moment-tag">
                        <u-icon name="map" size="16" color="#8966ef"></u-icon>
                        <text>上海市</text>
                    </view>
                </view> -->
                <text class="moment-title" v-if="workInfo.title">{{ workInfo.title }}</text>
                <text class="moment-text">{{ workInfo.content }}</text>
                <view class="moment-images" v-if="workInfo.files && workInfo.files.length > 0">
                    <!-- 单张图片显示 -->
                    <view class="image-layout single-image" v-if="workInfo.files.length === 1">
                        <view class="image-wrapper" @click="previewImage(0)">
                            <image :src="workInfo.files[0]" mode="widthFix" class="single-img"></image>
                        </view>
                    </view>

                    <!-- 2-3张图片横向显示 -->
                    <view class="image-layout double-image" v-else-if="workInfo.files.length >= 2 && workInfo.files.length <= 3">
                        <view v-for="(image, index) in workInfo.files.slice(0, 3)" :key="index" class="image-wrapper horizontal" @click="previewImage(index)">
                            <image :src="image" mode="aspectFill"></image>
                        </view>
                    </view>

                    <!-- 4张图片2x2显示 -->
                    <view class="image-layout four-image" v-else-if="workInfo.files.length === 4">
                        <view v-for="(image, index) in workInfo.files.slice(0, 4)" :key="index" class="image-wrapper" @click="previewImage(index)">
                            <image :src="image" mode="aspectFill"></image>
                        </view>
                    </view>

                    <!-- 5-9张图片九宫格显示 -->
                    <view class="image-layout grid-image" v-else>
                        <view v-for="(image, index) in workInfo.files.slice(0, 9)" :key="index" class="image-wrapper" @click="previewImage(index)">
                            <image :src="image" mode="aspectFill"></image>
                            <view class="image-count" v-if="index === 8 && workInfo.files.length > 9">
                                <text>+{{ workInfo.files.length - 9 }}</text>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="moment-footer">
                    <text class="moment-time">{{ workInfo.addtime }}</text>
                    <view class="moment-actions">
                        <!-- 评论 -->
                        <view class="action-item" hover-class="action-hover" @click="openCommentInput">
                            <u-icon name="chat" size="40" color="#8966ef"></u-icon>
                            <text class="action-text">({{ totalCommentCount }})</text>
                        </view>
                    </view>
                </view>

                <view class="comments-section">
                    <!-- 评论区标题栏 -->
                    <view class="comment-header-bar">
                        <view class="comment-header-left">
                            <u-icon name="chat-fill" size="22" color="#8966ef"></u-icon>
                            <text class="comment-title">评论</text>
                            <text class="comment-count">{{ totalCommentCount }}</text>
                        </view>
                    </view>

                    <!-- 输入引导区 -->
                    <view class="comment-input-guide" @click="openCommentInput">
                        <u-icon name="edit-pen" size="24" color="#999"></u-icon>
                        <text>说点什么...</text>
                    </view>

                    <!-- 评论加载状态 -->
                    <view class="loading-container" v-if="isCommentLoading && commentPage === 1">
                        <u-loading mode="circle" size="28" color="#8966ef"></u-loading>
                        <text class="loading-text">加载评论中...</text>
                    </view>

                    <!-- 评论列表 -->
                    <view v-if="commentsList.length > 0" class="comments-list">
                        <!-- 主评论项 -->
                        <view
                            v-for="(comment, index) in commentsList"
                            :key="comment.id"
                            class="comment-item"
                            :class="{
                                'comment-item-last': index === commentsList.length - 1 && !hasMoreComments,
                                'comment-item-new': comment.isNew,
                            }"
                        >
                            <!-- 评论内容卡片 -->
                            <view class="comment-card">
                                <!-- 用户头像 -->
                                <view class="comment-avatar">
                                    <u-avatar v-if="comment.user" :src="comment.user.headimgurl" size="60" bg-color="#8966ef" font-size="28"></u-avatar>
                                    <u-avatar v-else size="60"></u-avatar>
                                </view>

                                <!-- 评论右侧内容 -->
                                <view class="comment-right">
                                    <!-- 用户名和时间 -->
                                    <view class="comment-header">
                                        <text class="comment-username" v-if="comment.user">{{ comment.user.nickname }}</text>
                                        <text class="comment-time">{{ $util.formatTimeString(comment.addtime) }}</text>
                                    </view>

                                    <!-- 回复目标 -->
                                    <view class="reply-target" v-if="comment.parent_id === 0 && comment.reply_to">
                                        <text>回复</text>
                                        <text class="reply-name">@{{ comment.reply_to.nickname }}</text>
                                    </view>

                                    <!-- 评论内容 -->
                                    <view class="comment-text">
                                        {{ comment.content }}
                                    </view>

                                    <!-- 操作按钮区 -->
                                    <view class="comment-actions">
                                        <view class="action-btn" @click="handleReply(comment)">
                                            <u-icon name="chat" size="28" color="#999"></u-icon>
                                            <text>回复</text>
                                        </view>
                                    </view>

                                    <!-- 子评论区域 -->
                                    <view v-if="comment.child_num > 0" class="child-comments-container">
                                        <!-- 子评论展开/收起控制 -->
                                        <view class="child-comments-control" @click="toggleCommentReplies(comment)" v-if="!isCommentExpanded(comment.id)">
                                            <view class="expand-btn">
                                                <u-icon name="arrow-down" size="20" color="#8966ef"></u-icon>
                                                <text>{{ comment.child_num }}条回复</text>
                                            </view>
                                        </view>

                                        <!-- 子评论列表 -->
                                        <view v-if="isCommentExpanded(comment.id)" class="child-comments">
                                            <!-- 子评论加载状态 -->
                                            <view v-if="comment.isLoadingReplies" class="child-loading">
                                                <u-loading mode="circle" size="20" color="#8966ef"></u-loading>
                                                <text>加载回复中...</text>
                                            </view>

                                            <!-- 子评论列表 -->
                                            <view v-else-if="comment.child_list && comment.child_list.length > 0" class="child-comments-list">
                                                <view v-for="(reply, replyIndex) in comment.child_list" :key="reply.id" class="child-comment-item">
                                                    <!-- 子评论头像 -->
                                                    <view class="child-avatar">
                                                        <u-avatar v-if="reply.user" :src="reply.user.headimgurl" size="56" bg-color="#8966ef" font-size="24"></u-avatar>
                                                        <u-avatar v-else size="56"></u-avatar>
                                                    </view>

                                                    <!-- 子评论内容 -->
                                                    <view class="child-comment-content">
                                                        <!-- 子评论用户信息行 -->
                                                        <view class="child-comment-header">
                                                            <text class="child-username">{{ reply.user.nickname || '微信用户' }}</text>
                                                            <text v-if="reply.ai_user_list && reply.ai_user_list.length > 0" class="child-reply-info">
                                                                回复了
                                                                <text class="reply-name">@{{ reply.ai_user_list[0].nickname }}</text>
                                                            </text>
                                                            <text v-else-if="reply.reply_to" class="child-reply-info">
                                                                回复了
                                                                <text class="reply-name">@{{ reply.reply_to.nickname }}</text>
                                                            </text>
                                                        </view>

                                                        <!-- 子评论内容行 -->
                                                        <view class="child-comment-text">{{ reply.content }}</view>

                                                        <!-- 子评论操作栏 -->
                                                        <view class="child-comment-actions">
                                                            <text class="child-comment-time">{{ $util.formatTimeString(reply.addtime) }}</text>
                                                            <view class="child-action-btn" @click="handleReply(reply)">
                                                                <!-- <u-icon name="chat" size="20" color="#999"></u-icon> -->
                                                                <text>回复</text>
                                                            </view>
                                                        </view>
                                                    </view>
                                                </view>
                                            </view>

                                            <!-- 无子评论提示 -->
                                            <view v-else class="no-replies">
                                                <text>暂无回复</text>
                                            </view>

                                            <!-- 收起按钮 -->
                                            <view class="fold-replies" @click="toggleCommentReplies(comment)">
                                                <view class="fold-btn">
                                                    <u-icon name="arrow-up" size="20" color="#8966ef"></u-icon>
                                                    <text>收起回复</text>
                                                </view>
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>

                    <!-- 加载更多评论 -->
                    <view v-if="hasMoreComments && commentsList.length > 0" class="load-more">
                        <view class="load-more-btn" @click="loadMoreComments" :class="{ loading: isCommentLoading && commentPage > 1 }">
                            <u-loading v-if="isCommentLoading && commentPage > 1" mode="circle" size="24" color="#8966ef"></u-loading>
                            <text>{{ isCommentLoading && commentPage > 1 ? '加载中...' : '查看更多评论' }}</text>
                        </view>
                    </view>

                    <!-- 无评论提示 -->
                    <view v-if="!isCommentLoading && commentsList.length === 0" class="empty-comments">
                        <u-empty mode="comment" text="暂无评论，快来说点什么吧~"></u-empty>
                    </view>
                </view>
            </view>
        </view>

        <!-- 添加评论输入区域 -->
        <view class="comment-input-area" :class="{ 'comment-input-active': showCommentInput }" :style="{ bottom: keyboardHeight + 'px' }">
            <view class="comment-input-container">
                <!-- 回复状态提示 -->
                <view class="reply-status" v-if="replyToComment">
                    <text class="reply-status-text">
                        回复:
                        <text class="reply-name">@{{ replyToComment.user.nickname }}</text>
                    </text>
                    <view class="cancel-reply" @click="cancelReply">
                        <u-icon name="close" size="24" color="#999"></u-icon>
                    </view>
                </view>

                <view class="input-wrapper">
                    <input
                        class="comment-textarea"
                        v-model="commentText"
                        :focus="commentFocus"
                        :placeholder="replyToComment ? '回复 @' + replyToComment.user.nickname + '...' : '请输入评论内容...'"
                        :show-confirm-bar="false"
                        :adjust-position="false"
                        @blur="onCommentBlur"
                        :disabled="isSubmitting"
                        @keyboardheightchange="onKeyboardHeightChange"
                    />
                    <button
                        class="send-btn"
                        :disabled="!commentText.trim() || isSubmitting"
                        :class="{
                            'send-btn-active': commentText.trim() && !isSubmitting,
                            loading: isSubmitting,
                        }"
                        @click="addComment"
                    >
                        <u-loading v-if="isSubmitting" mode="circle" size="18" color="#ffffff"></u-loading>
                        <text v-else>发送</text>
                    </button>
                </view>
            </view>
        </view>

        <vip-popup v-model="showVip" @single-pay="handleSinglePay" @member-pay="handleMemberPay" @close="handleClose"></vip-popup>
        <gift-chat-popup v-model="showGiftChat" :work-id="workInfo.id" :gift-good-ids="giftGoodIds" @send-gift-success="handleSendGiftSuccess" @close="handleClose"></gift-chat-popup>
        <reward-popup v-model="showReward" :work-id="workInfo.id" :author-info="workInfo.user" @reward-success="handleRewardSuccess" @close="handleClose"></reward-popup>
        <login-popup v-model="showLoginPopup" @close="handleLoginPopupClose" @login="handleLoginAction"></login-popup>
        <wechat-info-popup v-model="showWechatInfoPopup" :user-info="workInfo.user" @close="handleWechatInfoClose"></wechat-info-popup>
    </view>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import vipPopup from '@/components/vip-popup.vue';
import giftChatPopup from '@/components/gift-chat-popup.vue';
import rewardPopup from '@/components/reward-popup.vue';
import loginPopup from '@/components/login-popup.vue';
import wechatInfoPopup from '@/components/wechat-info-popup.vue';
export default {
    components: {
        vipPopup,
        giftChatPopup,
        rewardPopup,
        loginPopup,
        wechatInfoPopup,
    },
    data() {
        return {
            showVip: false,
            showGiftChat: false,
            showReward: false,
            showLoginPopup: false,
            showWechatInfoPopup: false,
            // 用户信息
            workInfo: {},
            // 礼物商品ID配置
            giftGoodIds: {
                flower5: 1,
                flower10: 2,
                flower20: 3,
            },
            commentsList: [],
            id: '',
            // 添加评论相关数据
            commentText: '', // 评论内容
            replyToComment: null, // 回复的评论对象，null表示不是回复
            showCommentInput: false, // 控制输入框显示
            commentFocus: false, // 控制输入框焦点
            // 新增评论相关状态变量
            isCommentLoading: false, // 评论加载状态
            commentPage: 1, // 评论页码
            hasMoreComments: true, // 是否还有更多评论
            expandedCommentIds: [], // 已展开的评论ID列表
            commentTotalCount: 0, // 评论总数
            isSubmitting: false, // 评论提交状态
            keyboardHeight: 0, // 键盘高度
        };
    },
    computed: {
        ...mapState(['userInfo', 'hasLogin']),
        // 判断是否为自己的动态
        isOwnMoment() {
            return this.userInfo && this.workInfo?.user?.uid && this.userInfo.uid === this.workInfo.user.uid;
        },
        // 判断是否为有效会员
        isValidMember() {
            if (!this.userInfo || !this.userInfo.member_time) {
                return false;
            }

            // 如果member_time为0或空字符串，表示非会员
            if (this.userInfo.member_time === '0' || this.userInfo.member_time === 0 || this.userInfo.member_time === '') {
                return false;
            }

            // 将member_time转换为时间戳进行比较
            let memberExpireTime;

            // 如果member_time是时间戳格式（数字）
            if (typeof this.userInfo.member_time === 'number' || /^\d+$/.test(this.userInfo.member_time)) {
                memberExpireTime = parseInt(this.userInfo.member_time) * 1000; // 转换为毫秒
            }
            // 如果member_time是日期字符串格式
            else if (typeof this.userInfo.member_time === 'string') {
                memberExpireTime = new Date(this.userInfo.member_time.replace(/-/g, '/')).getTime();
            } else {
                return false;
            }

            // 比较当前时间和会员到期时间
            const currentTime = new Date().getTime();
            return memberExpireTime > currentTime;
        },
        // 微信号中间用***遮盖
        maskedWechat() {
            const wechat = this.workInfo?.user?.weixin || '';
            // if (wechat.length <= 4) return wechat;
            const start = wechat.substring(0, 2);
            const end = wechat.substring(wechat.length - 2);
            return `${start}******${end}`;
        },
        // 计算总评论数（包含子评论）
        totalCommentCount() {
            // 从API获取的主评论总数
            let mainCommentTotal = Number(this.commentTotalCount) || 0;
            // 计算当前已加载的子评论总数
            let childCommentTotal = 0;
            this.commentsList.forEach(comment => {
                if (comment.child_num) {
                    childCommentTotal += Number(comment.child_num);
                }
            });
            // 如果API返回的总数小于当前已加载的主评论数，说明API数据不准确，使用本地计算
            if (mainCommentTotal < this.commentsList.length) {
                mainCommentTotal = this.commentsList.length;
            }

            // 返回主评论数 + 子评论数
            return Number(mainCommentTotal + childCommentTotal);
        },
        // 判断性别
        isMale() {
            return this.workInfo?.user?.sex === '1';
        },
        // 计算年龄
        formatAge() {
            if (!this.workInfo?.user?.birthday) return '';
            const birthDate = new Date(this.workInfo.user.birthday.replace(/-/g, '/'));
            const today = new Date();
            let age = today.getFullYear() - birthDate.getFullYear();
            const monthDiff = today.getMonth() - birthDate.getMonth();
            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                age--;
            }
            return age;
        },
        // 格式化地理位置
        formatLocation() {
            if (!this.workInfo?.user) return '';
            const province = this.workInfo.user.province_code_txt?.name || '';
            const city = this.workInfo.user.city_code_txt?.name || '';
            const area = this.workInfo.user.area_code_txt?.name || '';

            if (province && city && area && province !== city && city !== area) {
                return `${city} ${area}`;
            } else if (province && city && province !== city) {
                return city;
            } else if (province) {
                return province;
            }
            return '未知地区';
        },
        // 只显示城市名称
        cityOnly() {
            if (!this.workInfo?.user?.city_code_txt?.name) {
                return this.workInfo?.user?.province_code_txt?.name || '未知地区';
            }

            const city = this.workInfo.user.city_code_txt.name;
            // 如果城市名称包含"自治区"等字样，使用区域名称
            if (city.includes('自治区') || city.includes('行政区划')) {
                return this.workInfo.user.area_code_txt?.name || city;
            }
            return city;
        },
        // 判断是否有标签
        hasTags() {
            return this.workInfo?.user?.tag_data_arr && this.workInfo.user.tag_data_arr.length > 0;
        },
        // 计算年代标识（80后、90后等）
        generationLabel() {
            if (!this.workInfo?.user?.birthday) return '';
            const birthDate = new Date(this.workInfo.user.birthday.replace(/-/g, '/'));
            const birthYear = birthDate.getFullYear();

            if (birthYear >= 1980 && birthYear < 1985) {
                return '80+';
            } else if (birthYear >= 1985 && birthYear < 1990) {
                return '85+';
            } else if (birthYear >= 1990 && birthYear < 1995) {
                return '90+';
            } else if (birthYear >= 1995 && birthYear < 2000) {
                return '95+';
            } else if (birthYear >= 2000) {
                return '00+';
            } else if (birthYear >= 1975) {
                return '75+';
            } else if (birthYear >= 1970) {
                return '70+';
            }
            return '';
        },
        // 完整地理位置：优先使用address字段，不存在则使用用户信息的地址
        // 统一格式：市-区，市取前四位不含"市"字，区取2位
        // 特殊处理：新疆、内蒙古等自治区显示为"自治区-城市"
        fullLocation() {
            // 优先使用address字段，但也要格式化
            if (this.workInfo?.address) {
                return this.formatAddressString(this.workInfo.address);
            }

            // address不存在，使用用户信息的地址
            return this.formatUserLocation();
        },

        
    },
    onLoad(e) {
        // 页面加载时的逻辑
        // uni.$u.config.unit = 'rpx';
        this.id = e.id;
        this.getWorkDetail();
    },
    methods: {
        ...mapActions(['VAgetUser']),
        async getWorkDetail() {
            const res = await this.$api.getWorkDetail({
                work_id: this.id,
                access_token: uni.getStorageSync('token'),
            });
            this.workInfo = res.data.info;
            this.getWorkCommentList();
        },
        // 方法定义
        previewImage(index) {
            if (!this.workInfo.files || this.workInfo.files.length === 0) return;

            const urls = this.workInfo.files;
            uni.previewImage({
                urls: urls,
                current: urls[index],
            });
        },
        showVipPopup() {
            if (!this.hasLogin) {
                this.showLoginPopup = true;
                return;
            }

            // 检查是否已经是会员
            if (this.isValidMember) {
                // 如果已经是会员，直接显示微信号
                this.showWechatInfo();
                return;
            }

            this.showVip = true;
        },

        // 显示微信号信息
        showWechatInfo() {
            this.showWechatInfoPopup = true;
        },

        // 处理单次支付
        async handleSinglePay(paymentData) {
            console.log('单次支付:', paymentData);

            try {
                uni.showLoading({
                    title: '处理中...',
                });

                // 这里应该调用单次付费的API
                // 由于没有具体的单次付费API，暂时模拟成功
                await new Promise(resolve => setTimeout(resolve, 2000));

                uni.hideLoading();
                uni.showToast({
                    title: '支付成功',
                    icon: 'success',
                });

                // 支付成功后显示微信号
                setTimeout(() => {
                    this.showWechatInfo();
                }, 1500);
            } catch (error) {
                console.error('单次支付失败:', error);
                uni.hideLoading();
                uni.showToast({
                    title: '支付失败，请重试',
                    icon: 'none',
                });
            }
        },

        // 处理会员支付
        async handleMemberPay(paymentData) {
            console.log('会员支付:', paymentData);

            try {
                uni.showLoading({
                    title: '处理中...',
                });

                // 创建会员订单
                const orderRes = await this.$api.createMemberOrder({
                    good_id: paymentData.product.id,
                    num: 1,
                    access_token: uni.getStorageSync('token'),
                });

                if (orderRes.status !== 0) {
                    throw new Error(orderRes.msg || '创建订单失败');
                }

                // 获取用户openid（小程序支付需要）
                const openid = this.userInfo.openid;
                if (!openid) {
                    throw new Error('获取用户信息失败，请重新登录');
                }

                // 调用支付接口
                const payRes = await this.$api.createMiniPay({
                    openid: openid,
                    orderid: orderRes.data.order_no,
                    access_token: uni.getStorageSync('token'),
                });

                if (payRes.status !== 0) {
                    throw new Error(payRes.msg || '支付失败');
                }

                // 调用微信支付
                await this.requestPayment(payRes.data);

                uni.hideLoading();
                uni.showToast({
                    title: '开通成功',
                    icon: 'success',
                });

                // 更新用户信息
                await this.VAgetUser();

                // 支付成功后显示微信号
                setTimeout(() => {
                    this.showWechatInfo();
                }, 1500);
            } catch (error) {
                console.error('会员支付失败:', error);
                uni.hideLoading();
                uni.showToast({
                    title: error.message || '支付失败，请重试',
                    icon: 'none',
                });
            }
        },

        // 调用微信支付
        async requestPayment(paymentData) {
            return new Promise((resolve, reject) => {
                uni.requestPayment({
                    provider: 'wxpay',
                    timeStamp: paymentData.timeStamp,
                    nonceStr: paymentData.nonceStr,
                    package: paymentData.package,
                    signType: paymentData.signType,
                    paySign: paymentData.paySign,
                    success: res => {
                        console.log('支付成功:', res);
                        resolve(res);
                    },
                    fail: err => {
                        console.error('支付失败:', err);
                        reject(new Error('支付失败'));
                    },
                });
            });
        },

        handleClose() {
            this.showVip = false;
        },

        // 关闭微信信息弹窗
        handleWechatInfoClose() {
            this.showWechatInfoPopup = false;
        },

        async handleFollow() {
            if (!this.hasLogin) {
                this.showLoginPopup = true;
                return;
            }
            const res = await this.$api.followUser({
                follow_user_id: this.workInfo.user.uid,
                access_token: uni.getStorageSync('token'),
            });
            this.workInfo.is_follow = !this.workInfo.is_follow;
            uni.showToast({
                title: this.workInfo.is_follow ? '关注成功' : '取消关注',
                icon: 'success',
            });
        },
        // 点赞功能
        async handleLike() {
            if (!this.hasLogin) {
                this.showLoginPopup = true;
                return;
            }
            try {
                const res = await this.$api.likeWork({
                    work_id: this.id,
                    access_token: uni.getStorageSync('token'),
                });
                this.workInfo.is_like = !this.workInfo.is_like;
                uni.showToast({
                    title: this.workInfo.is_like ? '点赞成功' : '取消点赞',
                    icon: 'success',
                });
                uni.$emit('refreshLike');
            } catch (error) {
                console.error('点赞操作失败:', error);
                uni.showToast({
                    title: '操作失败，请重试',
                    icon: 'none',
                });
            }
        },
        async getWorkCommentList(refresh = true) {
            try {
                if (refresh) {
                    this.commentsList = [];
                    this.commentPage = 1;
                    this.hasMoreComments = true;
                    // 重置展开的评论ID列表，避免重复
                    this.expandedCommentIds = [];
                }

                if (!this.hasMoreComments) return;

                this.isCommentLoading = true;

                const res = await this.$api.getWorkCommentList({
                    work_id: this.id,
                    access_token: uni.getStorageSync('token'),
                    page: this.commentPage,
                    pagesize: 10,
                    parent_id: 0,
                });

                // 更新评论总数
                if (res.data.total !== undefined) {
                    this.commentTotalCount = res.data.total;
                }

                // 处理分页逻辑
                const newComments = res.data.list || [];
                this.hasMoreComments = newComments.length >= 10;

                if (refresh) {
                    this.commentsList = newComments;

                    // 自动展开第一条有子评论的评论
                    const firstCommentWithReplies = this.commentsList.find(comment => comment.child_num > 0);
                    if (firstCommentWithReplies) {
                        this.expandedCommentIds.push(firstCommentWithReplies.id);
                    }
                } else {
                    this.commentsList = [...this.commentsList, ...newComments];
                }

                // 预加载子评论数据（仅加载那些有子评论的）
                const commentsWithChildren = this.commentsList.filter(item => item.child_num > 0);

                // 使用Promise.all并行加载所有子评论
                await Promise.all(
                    commentsWithChildren.map(async comment => {
                        // 检查评论是否在已展开列表中，或是新添加的评论
                        const shouldLoadChildren = this.expandedCommentIds.includes(comment.id) || (refresh && this.commentPage === 1 && comment.child_num <= 3);

                        if (shouldLoadChildren) {
                            await this.loadCommentReplies(comment, true);
                        }
                    })
                );
            } catch (error) {
                console.error('获取评论列表失败:', error);
                uni.showToast({
                    title: '获取评论失败，请重试',
                    icon: 'none',
                    duration: 2000,
                });
            } finally {
                this.isCommentLoading = false;
            }
        },
        openCommentInput() {
            if (!this.hasLogin) {
                this.showLoginPopup = true;
                return;
            }
            this.showCommentInput = true;
            this.commentFocus = true;
        },
        onCommentBlur() {
            this.commentFocus = false;
            setTimeout(() => {
                this.showCommentInput = false;
            }, 200);
        },
        cancelReply() {
            this.replyToComment = null;
        },
        handleReply(comment) {
            if (!this.hasLogin) {
                this.showLoginPopup = true;
                return;
            }
            if (!this.userInfo.province_code) {
                uni.showModal({
                    title: '提示',
                    content: '请先完善用户信息后再评论',
                    confirmText: '去完善',
                    success: res => {
                        if (res.confirm) {
                            uni.navigateTo({
                                url: '/views/mine/myUpdate',
                            });
                        }
                    },
                });
                return;
            }

            // 检查是否回复自己的评论
            if (this.userInfo && comment.user && this.userInfo.uid === comment.user.uid) {
                uni.showToast({
                    title: '不能回复自己的评论',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }

            console.log('回复评论:', comment);
            this.replyToComment = comment;
            this.openCommentInput();
        },
        async addComment() {
            if (!this.commentText.trim() || this.isSubmitting) return;

            try {
                this.isSubmitting = true;

                const commentData = {
                    work_id: this.id,
                    content: this.commentText.trim(),
                    access_token: uni.getStorageSync('token'),
                };

                // 如果是回复，添加parent_id和reply_user_id
                if (this.replyToComment) {
                    // 如果回复的是子评论，需要使用主评论的ID作为parent_id
                    if (Number(this.replyToComment.parent_id) > 0) {
                        commentData.parent_id = this.replyToComment.parent_id;
                    } else {
                        commentData.parent_id = this.replyToComment.id;
                    }

                    // 添加被回复用户ID
                    commentData.ai_user_arr = this.replyToComment.user.uid;
                }

                await this.$api.addWorkComment(commentData);
                // 评论成功
                uni.showToast({
                    title: '评论成功',
                    icon: 'success',
                    duration: 2000,
                });

                // 清空输入内容
                this.commentText = '';

                // 关闭评论框
                this.showCommentInput = false;

                // 如果是回复子评论，确保父评论已展开
                if (this.replyToComment && this.replyToComment.parent_id) {
                    const parentComment = this.commentsList.find(c => c.id === this.replyToComment.parent_id);
                    if (parentComment && !this.isCommentExpanded(parentComment.id)) {
                        this.expandedCommentIds.push(parentComment.id);
                    }
                }

                // 评论成功后重置回复对象
                const wasReplyingToComment = !!this.replyToComment;
                this.replyToComment = null;

                // 刷新评论列表
                await this.getWorkCommentList();

                // 如果评论成功，让页面滚动到评论区
                this.$nextTick(() => {
                    // 使用延时确保DOM已更新
                    setTimeout(() => {
                        // 使用小程序的方式滚动到评论区
                        const query = uni.createSelectorQuery().in(this);
                        query
                            .select('.comments-section')
                            .boundingClientRect(data => {
                                if (data) {
                                    uni.pageScrollTo({
                                        scrollTop: data.top,
                                        duration: 300,
                                    });
                                }
                            })
                            .exec();
                    }, 300);
                });
            } catch (error) {
                console.error('评论提交出错:', error);
                uni.showToast({
                    title: '评论失败，请重试',
                    icon: 'none',
                    duration: 2000,
                });
            } finally {
                this.isSubmitting = false;
            }
        },
        // 加载更多评论
        async loadMoreComments() {
            if (this.isCommentLoading || !this.hasMoreComments) return;

            this.commentPage++;
            await this.getWorkCommentList(false);
        },
        // 加载特定评论的回复
        async loadCommentReplies(comment, initial = false) {
            if (!comment) return;

            try {
                // 如果是初始加载或者评论已展开，才加载子评论
                if (!initial && !this.expandedCommentIds.includes(comment.id)) {
                    return;
                }

                // 设置加载状态
                this.$set(comment, 'isLoadingReplies', true);

                const res = await this.$api.getWorkCommentList({
                    work_id: this.id,
                    access_token: uni.getStorageSync('token'),
                    page: 1,
                    pagesize: 50, // 一次性加载更多子评论，避免分页
                    parent_id: comment.id,
                });

                // 更新子评论
                let childComments = res.data.list || [];

                // 处理回复关系
                childComments = this.processReplyInfo(childComments, comment);

                // 按时间正序排序，让最新的评论在最下面
                childComments.sort((a, b) => {
                    const timeA = new Date(a.addtime).getTime();
                    const timeB = new Date(b.addtime).getTime();
                    return timeA - timeB; // 正序排序，早的在前，晚的在后
                });

                // 更新评论的子评论列表
                this.$set(comment, 'child_list', childComments);
                this.$set(comment, 'hasAllReplies', true); // 标记已加载全部回复
            } catch (error) {
                console.error('加载评论回复失败:', error);
                uni.showToast({
                    title: '加载回复失败',
                    icon: 'none',
                    duration: 2000,
                });
            } finally {
                // 清除加载状态
                this.$set(comment, 'isLoadingReplies', false);
            }
        },
        // 展开或折叠子评论
        async toggleCommentReplies(comment) {
            if (!comment || comment.child_num <= 0) return;

            const commentId = comment.id;
            const isExpanded = this.expandedCommentIds.includes(commentId);

            if (isExpanded) {
                // 折叠评论
                const index = this.expandedCommentIds.findIndex(id => id === commentId);
                if (index !== -1) {
                    this.expandedCommentIds.splice(index, 1);
                }
            } else {
                // 展开评论
                this.expandedCommentIds.push(commentId);

                // 如果还没有加载子评论，则加载
                if (!comment.child_list || comment.child_list.length === 0) {
                    await this.loadCommentReplies(comment);
                }
            }
        },
        // 判断评论是否已展开
        isCommentExpanded(commentId) {
            return this.expandedCommentIds.includes(commentId);
        },

        // 以防后端API不返回完整的reply_to信息，添加处理方法
        processReplyInfo(commentsList, parentComment = null) {
            if (!commentsList || commentsList.length === 0) return commentsList;

            return commentsList.map(comment => {
                // 确保每个子评论都有parent_id
                if (parentComment && parentComment.id && !comment.parent_id) {
                    comment.parent_id = parentComment.id;
                    console.log(`为评论ID ${comment.id} 设置parent_id: ${parentComment.id}`);
                }

                // 如果没有reply_to信息但有reply_user_id，则构建基础的reply_to
                if (!comment.reply_to && comment.reply_user_id) {
                    // 查找被回复的用户
                    let replyToUser = null;

                    // 如果回复的是主评论的用户
                    if (parentComment && parentComment.user.uid === comment.reply_user_id) {
                        replyToUser = parentComment.user;
                    }
                    // 寻找同一评论区域下的其他评论
                    else if (parentComment && parentComment.child_list) {
                        const foundComment = parentComment.child_list.find(c => c.user.uid === comment.reply_user_id);
                        if (foundComment) {
                            replyToUser = foundComment.user;
                        }
                    }

                    // 如果找到了被回复用户，设置reply_to
                    if (replyToUser) {
                        comment.reply_to = {
                            uid: replyToUser.uid,
                            nickname: replyToUser.nickname,
                        };
                    }
                }

                return comment;
            });
        },
        onKeyboardHeightChange(e) {
            this.keyboardHeight = e.detail.height || 0;

            if (this.keyboardHeight === 0 && !this.commentText.trim()) {
                // setTimeout(() => {
                //     this.showCommentInput = false;
                // }, 200);
            }
        },
        // 显示打赏弹窗
        showRewardPopup() {
            if (!this.hasLogin) {
                this.showLoginPopup = true;
                return;
            }

            // 检查是否是自己的作品
            if (this.workInfo.user && this.userInfo && this.workInfo.user.uid === this.userInfo.id) {
                uni.showToast({
                    title: '不能给自己的作品打赏',
                    icon: 'none',
                });
                return;
            }

            this.showReward = true;
        },

        // 打赏成功回调
        handleRewardSuccess(data) {
            console.log('打赏成功', data);

            uni.showToast({
                title: `成功赠送${data.gift.name}`,
                icon: 'none',
            });

            // 可以在这里更新作品的打赏统计
            // 或者刷新作品详情
            // this.getWorkDetail();
        },

        // 聊天礼物成功回调
        handleSendGiftSuccess(data) {
            console.log('聊天礼物赠送成功', data);

            uni.showToast({
                title: '礼物赠送成功',
                icon: 'success',
            });
        },

        // 前往用户主页
        goToUserProfile() {
            if (this.workInfo && this.workInfo.user && this.workInfo.user.uid) {
                uni.navigateTo({
                    url: `/views/mine/userProfile?uid=${this.workInfo.user.uid}&id=${this.id}&address=${this.workInfo.address}`,
                });
            }
        },

        // 处理私聊
        async handlePrivateChat() {
            if (!this.hasLogin) {
                this.showLoginPopup = true;
                return;
            }
            if (!this.workInfo?.user) {
                uni.showToast({
                    title: '用户信息不完整',
                    icon: 'none',
                });
                return;
            }
            if (!this.isValidMember) {
                this.showVip = true;
                return;
            }
            const res = await this.$api.initSession({
                access_token: uni.getStorageSync('token'),
                to_uid: this.workInfo.user.uid,
            });
            const sessionId = res.data.msg_session_id;
            // 导航到聊天页面
            uni.navigateTo({
                url: `/views/moments/chat?sessionId=${sessionId}&userId=${this.workInfo.user.uid}&username=${encodeURIComponent(this.workInfo.user.nickname)}&avatar=${encodeURIComponent(
                    this.workInfo.user.headimgurl || ''
                )}`,
            });
        },

        // 处理编辑动态
        handleEditMoment() {
            uni.showModal({
                title: '编辑动态',
                content: '是否要编辑这条动态？',
                success: res => {
                    if (res.confirm) {
                        // 导航到编辑页面
                        uni.navigateTo({
                            url: `/views/moments/edit?id=${this.id}`,
                        });
                    }
                },
            });
        },

        // 显示状态操作菜单
        showStatusActionSheet() {
            const currentStatus = this.workInfo.status;
            const newStatus = Number(currentStatus) === 1 ? 2 : 1; // 在展示(1)和隐藏(2)之间切换
            const statusText = this.getStatusTextByValue(newStatus);

            uni.showModal({
                title: '确认操作',
                content: `确定要${statusText}这个作品吗？`,
                success: async res => {
                    if (res.confirm) {
                        try {
                            await this.$api.hideWork({
                                work_id: this.id,
                                access_token: uni.getStorageSync('token'),
                            });

                            // 更新本地状态
                            this.workInfo.status = newStatus;

                            uni.showToast({
                                title: `${statusText}成功`,
                                icon: 'success',
                            });
                        } catch (error) {
                            console.error('状态变更失败:', error);
                            uni.showToast({
                                title: '操作失败，请重试',
                                icon: 'none',
                            });
                        }
                    }
                },
            });
        },

        // 获取状态文本
        getStatusText() {
            // const status = Number(this.workInfo.status) === 1 ? 2 : 1 ;
            const status = Number(this.workInfo.status) === 1 ? '隐藏' : '展示';
            return status;
            // return this.getStatusTextByValue(status);
        },

        // 根据状态值获取文本
        getStatusTextByValue(status) {
            switch (Number(status)) {
                case 1:
                    return '展示';
                case 2:
                    return '隐藏';
                case 3:
                    return '下架';
                default:
                    return '展示';
            }
        },
        // 格式化地址字符串，统一为"市-区"格式
        formatAddressString(address) {
            // if (!address) return '';

            // // 如果地址已经是"市-区"格式，直接返回
            // if (address.includes('-') && address.split('-').length === 2) {
            //     const parts = address.split('-');
            //     let cityPart = parts[0].trim();
            //     let areaPart = parts[1].trim();

            //     // 处理市名称
            //     if (cityPart.includes('市')) {
            //         cityPart = cityPart.replace('市', '');
            //     }
            //     cityPart = cityPart.substring(0, 4);

            //     // 处理区名称
            //     areaPart = areaPart.substring(0, 2);

            //     return cityPart + '-' + areaPart;
            // }

            // 如果是完整地址，尝试提取市区信息
            return this.parseFullAddress(address);
        },
        // 格式化用户信息中的地址
        formatUserLocation() {
            if (!this.workInfo?.user) return '未知地区';

            const user = this.workInfo.user;
            let result = '';

            // 处理省/自治区信息
            let provinceName = '';
            if (user.province_code_txt && user.province_code_txt.name) {
                provinceName = user.province_code_txt.name;
            }

            // 处理市信息
            if (user.city_code_txt && user.city_code_txt.name) {
                let cityName = user.city_code_txt.name;
                let areaName = user.area_code_txt?.name || '';

                // 特殊处理自治区
                if (provinceName.includes('新疆') || provinceName.includes('内蒙古') || provinceName.includes('西藏') || provinceName.includes('宁夏') || provinceName.includes('广西')) {
                    // 自治区名称处理
                    let regionName = provinceName;
                    if (regionName.includes('维吾尔自治区')) {
                        regionName = '新疆';
                    } else if (regionName.includes('内蒙古自治区')) {
                        regionName = '内蒙古';
                    } else if (regionName.includes('西藏自治区')) {
                        regionName = '西藏';
                    } else if (regionName.includes('宁夏回族自治区')) {
                        regionName = '宁夏';
                    } else if (regionName.includes('广西壮族自治区')) {
                        regionName = '广西';
                    }

                    // 城市名称处理
                    if (cityName.includes('市')) {
                        cityName = cityName.replace('市', '');
                    }
                    if (cityName.includes('地区')) {
                        cityName = cityName.replace('地区', '');
                    }
                    if (cityName.includes('州')) {
                        cityName = cityName.replace('州', '');
                    }
                    // 区名称处理
                    if (areaName.includes('市')) {
                        areaName = areaName.replace('市', '');
                    }
                    if (areaName.includes('地区')) {
                        areaName = areaName.replace('地区', '');
                    }
                    if (areaName.includes('州')) {
                        areaName = areaName.replace('州', '');
                    }

                    result = regionName + '-' + areaName;
                } else {
                    // 普通省市处理
                    if (cityName.includes('市')) {
                        cityName = cityName.replace('市', '');
                    }
                    result = cityName.substring(0, 4);

                    // 处理区信息
                    if (user.area_code_txt && user.area_code_txt.name) {
                        let areaName = user.area_code_txt.name;
                        // 取前2位
                        areaName = areaName.substring(0, 2);
                        result += '-' + areaName;
                    }
                }
            } else {
                result = '未知地区';
            }

            return result;
        },

        // 处理删除动态
        handleDeleteMoment() {
            uni.showModal({
                title: '删除动态',
                content: '确定要删除这条动态吗？删除后无法恢复。',
                confirmColor: '#ff4757',
                success: async res => {
                    if (res.confirm) {
                        try {
                            await this.$api.deleteWork({
                                work_id: this.id,
                                access_token: uni.getStorageSync('token'),
                            });

                            uni.showToast({
                                title: '删除成功',
                                icon: 'success',
                            });

                            // 返回上一页
                            setTimeout(() => {
                                uni.navigateBack();
                            }, 1500);
                        } catch (error) {
                            console.error('删除动态失败:', error);
                            uni.showToast({
                                title: '删除失败，请重试',
                                icon: 'none',
                            });
                        }
                    }
                },
            });
        },
// 解析完整地址，提取市区信息
        parseFullAddress(address) {
            if (!this.workInfo.address || !this.workInfo.address.trim()) return '';

            // 正则表达式匹配省市区结构
            const patterns = [
                // 自治区格式：自治区+市+区（优先匹配，避免被省格式误匹配）
                /(.+?自治区)(.+?市)(.+?[区县])/,
                // 完整格式：省+市+区
                /(.+?省)(.+?市)(.+?[区县])/,
                // 直辖市格式：市+区
                /^(北京市|上海市|天津市|重庆市)(.+?[区县])/,
                // 简化格式：市+区（没有省）
                /^(.+?市)(.+?[区县])/
            ];

            let cityName = '';
            let areaName = '';
            let provinceName = '';

            for (let pattern of patterns) {
                const match = this.workInfo.address.match(pattern);
                if (match) {
                    if (pattern.source.includes('省')) {
                        provinceName = match[1] || '';
                        cityName = match[2] || '';
                        areaName = match[3] || '';
                    } else if (pattern.source.includes('自治区')) {
                        provinceName = match[1] || '';
                        cityName = match[2] || '';
                        areaName = match[3] || '';
                    } else if (pattern.source.includes('北京市|上海市|天津市|重庆市')) {
                        cityName = match[1] || '';
                        areaName = match[2] || '';
                    } else {
                        cityName = match[1] || '';
                        areaName = match[2] || '';
                    }
                    break;
                }
            }

            // 如果没有匹配到，尝试简单的文本分析
            if (!cityName || !areaName) {
                const cityIndex = this.workInfo.address.indexOf('市');
                const areaIndex = Math.max(this.workInfo.address.indexOf('区'), this.workInfo.address.indexOf('县'));

                if (cityIndex > 0 && areaIndex > cityIndex) {
                    let startIndex = 0;
                    const provinceIndex = Math.max(this.workInfo.address.indexOf('省'), this.workInfo.address.indexOf('自治区'));
                    if (provinceIndex >= 0) {
                        startIndex = provinceIndex + (this.workInfo.address.indexOf('省') >= 0 ? 1 : 3);
                    }

                    cityName = this.workInfo.address.substring(startIndex, cityIndex + 1);
                    areaName = this.workInfo.address.substring(cityIndex + 1, areaIndex + 1);
                }
            }

            // 格式化处理
            if (cityName && areaName) {
                // 处理市名称
                if (cityName.includes('市')) {
                    cityName = cityName.replace('市', '');
                }
                cityName = cityName.substring(0, 4);

                // 处理区名称
                if (areaName.includes('区')) {
                    areaName = areaName.replace('区', '');
                } else if (areaName.includes('县')) {
                    areaName = areaName.replace('县', '');
                }
                areaName = areaName.substring(0, 2);

                // 特殊处理自治区
                if (
                    provinceName &&
                    (provinceName.includes('新疆') || provinceName.includes('内蒙古') || provinceName.includes('西藏') || provinceName.includes('宁夏') || provinceName.includes('广西'))
                ) {
                    let regionName = provinceName;
                    if (regionName.includes('维吾尔自治区')) {
                        regionName = '新疆';
                    } else if (regionName.includes('内蒙古自治区')) {
                        regionName = '内蒙古';
                    } else if (regionName.includes('西藏自治区')) {
                        regionName = '西藏';
                    } else if (regionName.includes('宁夏回族自治区')) {
                        regionName = '宁夏';
                    } else if (regionName.includes('广西壮族自治区')) {
                        regionName = '广西';
                    }
                    return regionName + '-' + areaName;
                }

                return cityName + '-' + areaName;
            }

            // 如果解析失败，返回前8个字符作为备用
            return this.workInfo.address.substring(0, 8);
        },
        // 处理登录弹窗关闭
        handleLoginPopupClose() {
            this.showLoginPopup = false;
        },

        // 处理登录操作
        handleLoginAction() {
            // 登录弹窗已经会导航到登录页面，这里可以添加额外的逻辑
            console.log('用户点击了登录按钮');
        },
    },
};
</script>

<style lang="scss">
/* 定义全局变量 */
$primary-gradient: linear-gradient(135deg, #8966ef, #a584f2, #f0cc6c);
$primary-blue: #8966ef;
$primary-purple: #a584f2;
$primary-pink: #f0cc6c;
$gold-color: #f0cc6c;
$text-light: #ffffff;
$text-medium: rgba(255, 255, 255, 0.8);
$text-dark: #333333;
$bg-light: #f5f7fa;

/* 全局字体和间距优化 */
.moment-detail {
    background: linear-gradient(135deg, #f8f6ff 0%, #fff5e6 100%);
    min-height: 100vh;
    padding-bottom: 180rpx;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20rpx);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.content {
    padding: 30rpx 24rpx;
    background: linear-gradient(135deg, #f8f6ff 0%, #fff5e6 100%);
}

/* 导航栏样式 */
.custom-navbar {
    background: $primary-gradient !important;

    ::v-deep .u-navbar__content {
        background: $primary-gradient !important;
    }

    ::v-deep .u-navbar__content__title__text {
        color: $text-light !important;
    }

    ::v-deep .u-icon {
        color: $text-light !important;
    }
}

.online-status {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.2);
    padding: 6rpx 20rpx;
    border-radius: 30rpx;
}

.online-dot {
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
    background-color: $gold-color;
    margin-right: 10rpx;
    box-shadow: 0 0 10rpx rgba(255, 215, 0, 0.5);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.online-text {
    font-size: 24rpx;
    color: $text-light;
    font-weight: 500;
}

/* 个人信息区域 */
.profile-info {
    display: flex;
    flex-direction: column;
    background: linear-gradient(to right, #ffffff, #f9fbff);
    border-radius: 20rpx;
    padding: 24rpx 20rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 10rpx 30rpx rgba(137, 102, 239, 0.1);
    animation: slideIn 0.6s ease-out;
}

.user-info-section {
    display: flex;
    flex-direction: column;
    position: relative;
}

.user-main-info {
    display: flex;
    align-items: center;
}

.avatar-container {
    margin-right: 20rpx;
    position: relative;

    .custom-avatar {
        border: 4rpx solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 6rpx 20rpx rgba(74, 144, 226, 0.2);
    }
}

.info-container {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.user-nickname-row {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 12rpx;
}

.user-nickname {
    font-size: 34rpx;
    font-weight: 600;
    color: #333;
}

.online-time {
    font-size: 24rpx;
    color: #999;
    background: #f5f5f5;
    padding: 4rpx 12rpx;
    border-radius: 12rpx;
    margin-left: 12rpx;
}

.user-detail-row {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8rpx;
}

.gender-tag {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    width: 36rpx;
    height: 36rpx;

    &.male {
        background-color: #07c160;
    }

    &.female {
        background-color: #ff5597;
    }
}

/* 右上角点赞按钮样式 */
.like-button-container {
    // position: absolute;
    // top: 0;
    // right: 0;
    z-index: 10;
}

.like-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:active {
        transform: scale(0.95);
    }

    &.liked {
        background-color: rgba(255, 107, 107, 0.1);
        box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
    }
}

.info-tag {
    display: flex;
    align-items: center;
    background-color: rgba(137, 102, 239, 0.1);
    padding: 4rpx 12rpx;
    border-radius: 20rpx;
    max-width: 200rpx;
}

.info-tag text {
    font-size: 22rpx;
    color: #8966ef;
    margin-left: 4rpx;
}

.location-tag {
    max-width: 300rpx;
}

.location-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 250rpx;
}

/* 移除不再使用的样式 */

.divider {
    height: 1px;
    background: linear-gradient(to right, rgba(137, 102, 239, 0.2), rgba(165, 132, 242, 0.2), rgba(137, 102, 239, 0.1));
    margin: 16rpx 0;
    width: 100%;
}

.user-intro {
    font-size: 30rpx;
    color: #666;
    margin-top: 16rpx;
    line-height: 1.5;
    padding: 0 4rpx;
}

.user-tags {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 16rpx;
}

.tag-item {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #8966ef, #a584f2);
    padding: 6rpx 16rpx;
    border-radius: 24rpx;
    margin-right: 12rpx;
    // margin-bottom: 10rpx;
    box-shadow: 0 4rpx 10rpx rgba(137, 102, 239, 0.2);

    text {
        font-size: 22rpx;
        color: #ffffff;
        font-weight: 500;
    }
}

.action-buttons-section {
    margin-top: 16rpx;
}

.action-buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-end;
    gap: 20rpx;

    &.own-moment-actions {
        justify-content: flex-end;
        margin-top: 16rpx;
    }
    button {
        margin: 0;
        padding: 0;
    }
}

/* 渐变按钮样式 */
.gradient-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    // padding: 8rpx 16rpx;
    border-radius: 24rpx;
    border: none;
    color: $text-light;
    font-size: 24rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    width: 140rpx;

    text {
        margin-left: 6rpx;
        font-weight: 500;
    }

    &:active {
        transform: translateY(2rpx);
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
    }
}

/* 私聊按钮 */
.private-chat-btn {
    background: linear-gradient(135deg, #8966ef, #a584f2);
    box-shadow: 0 4rpx 12rpx rgba(137, 102, 239, 0.3);
}

/* 微信按钮 */
.wechat-btn {
    background: linear-gradient(135deg, #07c160, #10d56a);
    box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3);
}

/* 礼物按钮 */
.gift-btn {
    background: linear-gradient(135deg, #f0cc6c, #edc353);
    box-shadow: 0 4rpx 12rpx rgba(240, 204, 108, 0.3);
}

/* 打赏按钮 */
.reward-btn {
    background: linear-gradient(135deg, #8966ef, #a584f2);
    box-shadow: 0 4rpx 12rpx rgba(137, 102, 239, 0.3);
}

/* 关注按钮 */
.follow-btn {
    background: linear-gradient(135deg, #8966ef, #a584f2);
    box-shadow: 0 4rpx 12rpx rgba(137, 102, 239, 0.3);
}

/* 取消关注按钮 */
.unfollow-btn {
    background: linear-gradient(135deg, #999999, #777777);
    box-shadow: 0 4rpx 12rpx rgba(153, 153, 153, 0.3);
}

/* 点赞按钮 */
.like-btn {
    background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
    box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

/* 取消点赞按钮 */
.unlike-btn {
    background: linear-gradient(135deg, #999999, #777777);
    box-shadow: 0 4rpx 12rpx rgba(153, 153, 153, 0.3);
}

/* 编辑按钮 */
.edit-btn {
    background: linear-gradient(135deg, #8966ef, #a584f2);
    box-shadow: 0 4rpx 12rpx rgba(137, 102, 239, 0.3);
}

/* 状态按钮 */
.status-btn {
    background: linear-gradient(135deg, #8966ef, #a584f2);
    box-shadow: 0 4rpx 12rpx rgba(137, 102, 239, 0.3);
}

/* 删除按钮 */
.delete-btn {
    background: linear-gradient(135deg, #ff4757, #ff3742);
    box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.3);
}

.btn-hover {
    transform: translateY(2rpx);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
    opacity: 0.9;
}

/* 动态内容区域 */
.moment-content {
    background: linear-gradient(to bottom, #ffffff, #f9fbff);
    border-radius: 20rpx;
    padding: 22rpx 30rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 10rpx 30rpx rgba(137, 102, 239, 0.08);
    border-top: 6rpx solid $primary-purple;
    position: relative;
    overflow: hidden;
    animation: slideIn 0.6s ease-out;

    &::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 120rpx;
        height: 120rpx;
        background: radial-gradient(circle at top right, rgba(240, 204, 108, 0.1), transparent 70%);
        z-index: 1;
        pointer-events: none;
    }
}

.section-header {
    display: flex;
    align-items: center;
    padding-bottom: 24rpx;
    margin-bottom: 24rpx;
    border-bottom: 1rpx solid rgba(74, 144, 226, 0.2);
}

.section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: $text-dark;
    margin-left: 12rpx;
    letter-spacing: 1rpx;
}

.moment-tags {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20rpx;
}

.moment-tag {
    display: flex;
    align-items: center;
    background-color: rgba(137, 102, 239, 0.1);
    padding: 6rpx 16rpx;
    border-radius: 30rpx;
    margin-right: 16rpx;
    margin-bottom: 10rpx;
}

.moment-tag text {
    font-size: 24rpx;
    color: $primary-blue;
    margin-left: 6rpx;
}

.moment-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
    display: block;
    line-height: 1.5;
}

.moment-text {
    font-size: 32rpx;
    line-height: 1.7;
    color: #333;
    margin-bottom: 20rpx;
    display: block;
    letter-spacing: 0.5rpx;
}

.moment-images {
    margin-bottom: 30rpx;
    overflow: hidden;
}

/* 基础图片布局样式 */
.image-layout {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    overflow: hidden;
}

/* 图片容器通用样式 */
.image-wrapper {
    position: relative;
    overflow: hidden;
    border-radius: 8rpx;
    background-color: #f7f7f7;
    box-sizing: border-box;
    margin: 2rpx;

    image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    &:active image {
        transform: scale(1.03);
    }
}

/* 单张图片样式 */
.single-image {
    .image-wrapper {
        width: 48%;
        max-width: 500rpx;
        border-radius: 8rpx;

        .single-img {
            width: 100%;
            max-height: 800rpx;
            object-fit: contain;
        }
    }
}

/* 2-3张图片横向样式 */
.double-image {
    display: flex;
    flex-direction: row;

    .image-wrapper.horizontal {
        flex: 1;
        height: 220rpx;
        margin-right: 6rpx;

        &:last-child {
            margin-right: 0;
        }
    }
}

/* 4张图片2x2布局样式 */
.four-image {
    width: 100%;
    display: flex;
    flex-wrap: wrap;

    .image-wrapper {
        width: calc(50% - 4rpx);
        height: 240rpx;
        margin: 2rpx;
    }
}

/* 5-9张图片九宫格样式 */
.grid-image {
    width: 100%;
    display: flex;
    flex-wrap: wrap;

    .image-wrapper {
        width: calc(33.33% - 4rpx);
        height: 200rpx;
        margin: 2rpx;
        position: relative;

        .image-count {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.4);
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36rpx;
            font-weight: 500;
        }
    }
}

.moment-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid rgba(74, 144, 226, 0.2);
    margin-bottom: 20rpx;
}

.moment-time {
    font-size: 24rpx;
    color: #999;
}

.moment-actions {
    display: flex;
}

.action-item {
    display: flex;
    align-items: center;
    margin-left: 30rpx;
    padding: 6rpx 16rpx;
    border-radius: 30rpx;
    transition: all 0.3s;
}

.action-hover {
    background-color: rgba(137, 102, 239, 0.1);
}

.action-text {
    font-size: 24rpx;
    color: $primary-blue;
    margin-left: 6rpx;
}

.comments-section {
    margin-top: 30rpx;
    border-radius: 20rpx;
    background-color: #ffffff;
    padding: 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    position: relative;
}

.comment-header-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid #f5f5f5;
}

.comment-header-left {
    display: flex;
    align-items: center;
}

.comment-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin: 0 8rpx;
}

.comment-count {
    font-size: 28rpx;
    color: #999;
}

.comment-input-guide {
    display: flex;
    align-items: center;
    padding: 20rpx 24rpx;
    border-radius: 24rpx;
    background-color: #f8f9fa;
    margin-bottom: 20rpx;
    border: 1rpx solid #e9ecef;
    transition: all 0.3s ease;
}

.comment-input-guide:active {
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.comment-input-guide text {
    font-size: 28rpx;
    color: #6c757d;
    margin-left: 12rpx;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40rpx 0;
}

.loading-text {
    font-size: 26rpx;
    color: #999;
    margin-top: 16rpx;
}

.comments-list {
    margin-top: 20rpx;
}

.comment-item {
    padding: 10rpx 0;
    border-bottom: 1rpx solid #e9ecef;
}

.comment-item:last-child {
    border-bottom: none;
}

.comment-card {
    display: flex;
    align-items: flex-start;
}

.comment-avatar {
    margin-right: 20rpx;
    flex-shrink: 0;
}

.comment-right {
    flex: 1;
    min-width: 0;
}

.comment-header {
    display: flex;
    align-items: center;
    margin-bottom: 8rpx;
}

.comment-username {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    margin-right: 16rpx;
}

.comment-time {
    font-size: 24rpx;
    color: #999;
}

.reply-target {
    font-size: 26rpx;
    color: #666;
    margin-bottom: 8rpx;
}

.reply-name {
    color: #8966ef;
    font-weight: 500;
}

.comment-text {
    font-size: 30rpx;
    line-height: 1.5;
    color: #333;
    margin-bottom: 16rpx;
    word-break: break-all;
}

.comment-actions {
    display: flex;
    align-items: center;
}

.action-btn {
    display: flex;
    align-items: center;
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    background-color: #f5f5f5;
    transition: all 0.2s;
}

.action-btn:active {
    background-color: #e8e8e8;
}

.action-btn text {
    font-size: 24rpx;
    color: #666;
    margin-left: 6rpx;
}

.child-comments-container {
    margin-top: 16rpx;
}

.child-comments-control {
    padding: 16rpx 0 8rpx 0;
}

.expand-btn {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 8rpx 16rpx;
    background-color: #f8f9fa;
    border-radius: 20rpx;
    border: 1rpx solid #e9ecef;
    transition: all 0.2s;
    width: fit-content;
}

.expand-btn:active {
    background-color: #e9ecef;
    transform: scale(0.98);
}

.expand-btn text {
    font-size: 24rpx;
    color: #8966ef;
    margin-left: 8rpx;
    font-weight: 500;
}

.child-comments {
    // background-color: #fafbfc;
    // border-radius: 16rpx;
    padding: 10rpx 0;
    margin-top: 8rpx;
    margin-left: -20rpx;
    // border-left: 3rpx solid #e9ecef;
}

.child-comment-item {
    display: flex;
    align-items: flex-start;
    padding: 10rpx 0;
    border-bottom: 1rpx solid #f0f2f5;
}

.child-comment-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.child-comment-item:first-child {
    padding-top: 0;
}

.child-avatar {
    margin-right: 16rpx;
    flex-shrink: 0;
}

.child-comment-content {
    flex: 1;
    min-width: 0;
}

.child-comment-header {
    margin-bottom: 8rpx;
    line-height: 1.4;
}

.child-username {
    font-size: 26rpx;
    font-weight: 600;
    color: #333;
}

.child-reply-info {
    font-size: 24rpx;
    color: #666;
    margin-left: 8rpx;
}

.reply-name {
    color: #8966ef;
    font-weight: 500;
}

.child-comment-text {
    font-size: 28rpx;
    color: #333;
    word-break: break-all;
    line-height: 1.5;
    margin-bottom: 12rpx;
}

.child-comment-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.child-comment-time {
    font-size: 22rpx;
    color: #999;
}

.child-action-btn {
    display: flex;
    align-items: center;
    padding: 6rpx 12rpx;
    border-radius: 16rpx;
    background-color: #f0f2f5;
    transition: all 0.2s;
}

.child-action-btn:active {
    background-color: #e4e6ea;
}

.child-action-btn text {
    font-size: 22rpx;
    color: #666;
    margin-left: 4rpx;
}

.fold-replies {
    padding: 16rpx 0 8rpx 0;
    text-align: center;
}

.fold-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8rpx 16rpx;
    background-color: #f8f9fa;
    border-radius: 20rpx;
    border: 1rpx solid #e9ecef;
    transition: all 0.2s;
}

.fold-btn:active {
    background-color: #e9ecef;
    transform: scale(0.98);
}

.fold-btn text {
    font-size: 24rpx;
    color: #8966ef;
    margin-left: 8rpx;
    font-weight: 500;
}

.load-more {
    text-align: center;
    margin-top: 30rpx;
}

.load-more-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12rpx 36rpx;
    border-radius: 30rpx;
    background-color: #f5f7fa;
    color: #8966ef;
    font-size: 26rpx;
    box-shadow: 0 2rpx 8rpx rgba(137, 102, 239, 0.1);
    transition: all 0.3s;
}

.load-more-btn:active {
    background-color: #edf1f7;
    transform: translateY(2rpx);
}

.load-more-btn.loading {
    opacity: 0.8;
}

.empty-comments {
    padding: 50rpx 0;
    text-align: center;
}

/* 添加评论输入区域样式 */
.comment-input-area {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #ffffff;
    padding: 20rpx 24rpx;
    box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
    border-top: 1rpx solid #e9ecef;
    z-index: 999;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.comment-input-active {
    transform: translateY(0);
}

.comment-input-container {
    width: 100%;
}

.input-wrapper {
    display: flex;
    align-items: flex-end;
    width: 100%;
    gap: 16rpx;
}

.comment-textarea {
    flex: 1;
    background-color: #f8f9fa;
    border-radius: 24rpx;
    padding: 16rpx 24rpx;
    font-size: 28rpx;
    line-height: 1.5;
    border: 1rpx solid #e9ecef;
    transition: all 0.2s ease;
}

.comment-textarea:focus {
    border-color: #8966ef;
    background-color: #ffffff;
}

.send-btn {
    width: 120rpx;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 28rpx;
    color: #ffffff;
    background: linear-gradient(135deg, #dee2e6, #adb5bd);
    border-radius: 24rpx;
    text-align: center;
    padding: 0;
    position: relative;
    overflow: hidden;
    transition: all 0.2s ease;
}

.send-btn-active {
    background: linear-gradient(135deg, #8966ef, #a584f2);
    box-shadow: 0 4rpx 12rpx rgba(137, 102, 239, 0.3);
}

.send-btn.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
    animation: loading-shine 1.5s infinite;
}

@keyframes loading-shine {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* 添加回复状态样式 */
.reply-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12rpx 20rpx;
    background-color: #e3f2fd;
    border-radius: 12rpx;
    margin-bottom: 16rpx;
    border-left: 4rpx solid #8966ef;
}

.reply-status-text {
    font-size: 26rpx;
    color: #333;
}

.reply-status .reply-name {
    color: #8966ef;
    font-weight: 600;
}

.cancel-reply {
    padding: 8rpx;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.cancel-reply:active {
    background-color: rgba(0, 0, 0, 0.1);
}
</style>
